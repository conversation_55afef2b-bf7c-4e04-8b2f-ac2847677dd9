#!/usr/bin/env python3
"""
Comprehensive Evaluation Results Analysis Script

This script extracts and analyzes evaluation results from GPT and Gemini evaluator files,
providing detailed statistics and cross-evaluator comparisons.

Key Features:
- Extracts and cleans model names by removing UUID prefixes from evaluation identifiers
- Handles multi-response evaluations by averaging scores across choices/candidates per question
- Aggregates question-level averages to compute overall model statistics
- Supports multiple evaluator formats (GPT, Gemini, and additional files)
- Exports results to both text reports and CSV format
- Provides comprehensive statistics including mean, std dev, min/max, median, and count

Usage:
    python analyze_evaluation_results.py [--gpt-file PATH] [--gemini-file PATH] [--output PATH]
"""

import json
import re
import argparse
import sys
import csv
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import statistics


class EvaluationAnalyzer:
    """Analyzes evaluation results from GPT and Gemini evaluators."""
    
    def __init__(self):
        self.gpt_scores = defaultdict(list)  # model_name -> [all_individual_scores]
        self.gemini_scores = defaultdict(list)  # model_name -> [all_individual_scores]
        self.additional_scores = defaultdict(lambda: defaultdict(list))  # evaluator -> model_name -> [all_individual_scores]
        self.score_pattern = re.compile(r'\*\*Score:\s*(\d+(?:\.\d+)?)/10\*\*', re.IGNORECASE)
        
    def extract_score_from_text(self, text: str) -> Optional[float]:
        """Extract score from evaluation text using regex pattern."""
        matches = self.score_pattern.findall(text)
        if matches:
            try:
                # Take the last score found (typically the final score)
                return float(matches[-1])
            except ValueError:
                return None
        return None
    
    def extract_model_name(self, identifier: str) -> Optional[str]:
        """Extract and clean model name from custom_id or key field."""
        # Format: eval-{question_id}-{model_name}
        # where question_id is a UUID-like hash (e.g., 95891ae5-2289-52e2-ace0-823b1dfb435f)
        if not identifier.startswith('eval-'):
            return None

        # Remove 'eval-' prefix
        remaining = identifier[5:]

        # Split by hyphens and look for UUID pattern
        parts = remaining.split('-')
        if len(parts) >= 6:  # UUID has 5 parts, so we need at least 6 total parts
            # UUID pattern: 8-4-4-4-12 hex characters
            # Check if first 5 parts form a UUID pattern
            if (len(parts[0]) == 8 and len(parts[1]) == 4 and
                len(parts[2]) == 4 and len(parts[3]) == 4 and
                len(parts[4]) == 12):
                # Check if they're all hex
                uuid_parts = parts[:5]
                if all(all(c in '0123456789abcdef' for c in part.lower()) for part in uuid_parts):
                    # Model name is everything after the UUID
                    model_name = '-'.join(parts[5:])
                    return model_name

        # Fallback: if UUID pattern not found, use original simple split
        parts = remaining.split('-', 1)
        if len(parts) >= 2:
            return parts[1]

        return None

    def extract_and_average_scores(self, texts: List[str]) -> Optional[float]:
        """Extract ALL scores from multiple texts and return their average."""
        all_scores = []
        for text in texts:
            # Extract ALL scores from this text, not just the first one
            matches = self.score_pattern.findall(text)
            for match in matches:
                try:
                    score = float(match)
                    all_scores.append(score)
                except ValueError:
                    continue

        if all_scores:
            return sum(all_scores) / len(all_scores)
        return None

    def process_gpt_file(self, file_path: Path) -> int:
        """Process GPT evaluation file and extract scores."""
        processed_count = 0

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        data = json.loads(line.strip())

                        # Extract model name from custom_id
                        custom_id = data.get('custom_id', '')
                        model_name = self.extract_model_name(custom_id)

                        if not model_name:
                            continue

                        # Extract ALL scores from all choices for this evaluation
                        response = data.get('response', {})
                        body = response.get('body', {})
                        choices = body.get('choices', [])

                        choice_texts = []
                        for choice in choices:
                            message = choice.get('message', {})
                            content = message.get('content', '')
                            choice_texts.append(content)

                        # Extract all scores from all choices and add them individually
                        all_scores = []
                        for text in choice_texts:
                            matches = self.score_pattern.findall(text)
                            for match in matches:
                                try:
                                    score = float(match)
                                    all_scores.append(score)
                                except ValueError:
                                    continue

                        # Add all individual scores to the model's score list
                        for score in all_scores:
                            self.gpt_scores[model_name].append(score)
                            processed_count += 1

                    except json.JSONDecodeError as e:
                        print(f"Warning: JSON decode error in GPT file line {line_num}: {e}")
                        continue
                    except Exception as e:
                        print(f"Warning: Error processing GPT file line {line_num}: {e}")
                        continue

        except FileNotFoundError:
            print(f"Error: GPT file not found: {file_path}")
            return 0
        except Exception as e:
            print(f"Error: Failed to process GPT file: {e}")
            return 0

        return processed_count
    
    def process_gemini_file(self, file_path: Path) -> int:
        """Process Gemini evaluation file and extract scores."""
        processed_count = 0

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        data = json.loads(line.strip())

                        # Extract model name from key
                        key = data.get('key', '')
                        model_name = self.extract_model_name(key)

                        if not model_name:
                            continue

                        # Extract ALL scores from all candidates for this evaluation
                        response = data.get('response', {})
                        candidates = response.get('candidates', [])

                        candidate_texts = []
                        for candidate in candidates:
                            content = candidate.get('content', {})
                            parts = content.get('parts', [])

                            for part in parts:
                                text = part.get('text', '')
                                candidate_texts.append(text)

                        # Extract all scores from all candidates and add them individually
                        all_scores = []
                        for text in candidate_texts:
                            matches = self.score_pattern.findall(text)
                            for match in matches:
                                try:
                                    score = float(match)
                                    all_scores.append(score)
                                except ValueError:
                                    continue

                        # Add all individual scores to the model's score list
                        for score in all_scores:
                            self.gemini_scores[model_name].append(score)
                            processed_count += 1

                    except json.JSONDecodeError as e:
                        print(f"Warning: JSON decode error in Gemini file line {line_num}: {e}")
                        continue
                    except Exception as e:
                        print(f"Warning: Error processing Gemini file line {line_num}: {e}")
                        continue

        except FileNotFoundError:
            print(f"Error: Gemini file not found: {file_path}")
            return 0
        except Exception as e:
            print(f"Error: Failed to process Gemini file: {e}")
            return 0

        return processed_count

    def process_additional_file(self, file_path: Path, evaluator_name: str) -> int:
        """Process additional evaluation file (auto-detects format)."""
        processed_count = 0

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    try:
                        data = json.loads(line.strip())

                        # Try to detect format and extract model name
                        model_name = None
                        texts = []

                        # GPT-style format
                        if 'custom_id' in data:
                            custom_id = data.get('custom_id', '')
                            model_name = self.extract_model_name(custom_id)

                            response = data.get('response', {})
                            body = response.get('body', {})
                            choices = body.get('choices', [])

                            for choice in choices:
                                message = choice.get('message', {})
                                content = message.get('content', '')
                                texts.append(content)

                        # Gemini-style format
                        elif 'key' in data:
                            key = data.get('key', '')
                            model_name = self.extract_model_name(key)

                            response = data.get('response', {})
                            candidates = response.get('candidates', [])

                            for candidate in candidates:
                                content = candidate.get('content', {})
                                parts = content.get('parts', [])

                                for part in parts:
                                    text = part.get('text', '')
                                    texts.append(text)

                        # Extract all scores from all texts and add them individually
                        if model_name and texts:
                            all_scores = []
                            for text in texts:
                                matches = self.score_pattern.findall(text)
                                for match in matches:
                                    try:
                                        score = float(match)
                                        all_scores.append(score)
                                    except ValueError:
                                        continue

                            # Add all individual scores to the model's score list
                            for score in all_scores:
                                self.additional_scores[evaluator_name][model_name].append(score)
                                processed_count += 1

                    except json.JSONDecodeError as e:
                        print(f"Warning: JSON decode error in {evaluator_name} file line {line_num}: {e}")
                        continue
                    except Exception as e:
                        print(f"Warning: Error processing {evaluator_name} file line {line_num}: {e}")
                        continue

        except FileNotFoundError:
            print(f"Error: {evaluator_name} file not found: {file_path}")
            return 0
        except Exception as e:
            print(f"Error: Failed to process {evaluator_name} file: {e}")
            return 0

        return processed_count

    def calculate_statistics(self, scores: List[float]) -> Dict[str, float]:
        """Calculate comprehensive statistics for a list of scores."""
        if not scores:
            return {
                'count': 0,
                'average': 0.0,
                'std_dev': 0.0,
                'min': 0.0,
                'max': 0.0,
                'median': 0.0
            }
        
        return {
            'count': len(scores),
            'average': statistics.mean(scores),
            'std_dev': statistics.stdev(scores) if len(scores) > 1 else 0.0,
            'min': min(scores),
            'max': max(scores),
            'median': statistics.median(scores)
        }
    
    def generate_report(self) -> str:
        """Generate comprehensive analysis report."""
        report = []
        report.append("=" * 80)
        report.append("COMPREHENSIVE EVALUATION RESULTS ANALYSIS")
        report.append("=" * 80)
        report.append("")
        
        # Section 1: GPT Evaluations
        report.append("SECTION 1: GPT EVALUATIONS")
        report.append("-" * 40)
        report.append("")
        
        if not self.gpt_scores:
            report.append("No GPT evaluation data found.")
        else:
            # Sort models by average score (highest to lowest)
            gpt_models = sorted(self.gpt_scores.keys(), 
                              key=lambda m: statistics.mean(self.gpt_scores[m]), 
                              reverse=True)
            
            for model in gpt_models:
                scores = self.gpt_scores[model]
                stats = self.calculate_statistics(scores)
                
                report.append(f"Model: {model}")
                report.append(f"  Individual Scores: {sorted(scores, reverse=True)}")
                report.append(f"  Count: {stats['count']}")
                report.append(f"  Average: {stats['average']:.2f}")
                report.append(f"  Std Dev: {stats['std_dev']:.2f}")
                report.append(f"  Min/Max: {stats['min']:.1f} / {stats['max']:.1f}")
                report.append(f"  Median: {stats['median']:.1f}")
                report.append("")
        
        report.append("")
        
        # Section 2: Gemini Evaluations
        report.append("SECTION 2: GEMINI EVALUATIONS")
        report.append("-" * 40)
        report.append("")
        
        if not self.gemini_scores:
            report.append("No Gemini evaluation data found.")
        else:
            # Sort models by average score (highest to lowest)
            gemini_models = sorted(self.gemini_scores.keys(), 
                                 key=lambda m: statistics.mean(self.gemini_scores[m]), 
                                 reverse=True)
            
            for model in gemini_models:
                scores = self.gemini_scores[model]
                stats = self.calculate_statistics(scores)
                
                report.append(f"Model: {model}")
                report.append(f"  Individual Scores: {sorted(scores, reverse=True)}")
                report.append(f"  Count: {stats['count']}")
                report.append(f"  Average: {stats['average']:.2f}")
                report.append(f"  Std Dev: {stats['std_dev']:.2f}")
                report.append(f"  Min/Max: {stats['min']:.1f} / {stats['max']:.1f}")
                report.append(f"  Median: {stats['median']:.1f}")
                report.append("")
        
        report.append("")
        
        # Section 3: Cross-Evaluator Comparison
        report.append("SECTION 3: CROSS-EVALUATOR COMPARISON")
        report.append("-" * 40)
        report.append("")
        
        # Find common models
        common_models = set(self.gpt_scores.keys()) & set(self.gemini_scores.keys())
        
        if not common_models:
            report.append("No models found in both GPT and Gemini evaluations.")
        else:
            report.append("Models evaluated by both GPT and Gemini:")
            report.append("")
            
            # Create comparison table
            comparison_data = []
            for model in common_models:
                gpt_avg = statistics.mean(self.gpt_scores[model])
                gemini_avg = statistics.mean(self.gemini_scores[model])
                difference = gpt_avg - gemini_avg
                comparison_data.append((model, gpt_avg, gemini_avg, difference))
            
            # Sort by GPT average score (highest to lowest)
            comparison_data.sort(key=lambda x: x[1], reverse=True)
            
            # Table header
            report.append(f"{'Model':<40} {'GPT Avg':<10} {'Gemini Avg':<12} {'Difference':<12}")
            report.append("-" * 76)
            
            for model, gpt_avg, gemini_avg, diff in comparison_data:
                diff_str = f"{diff:+.2f}"
                report.append(f"{model:<40} {gpt_avg:<10.2f} {gemini_avg:<12.2f} {diff_str:<12}")
            
            report.append("")
            report.append("Summary Statistics:")
            
            gpt_averages = [x[1] for x in comparison_data]
            gemini_averages = [x[2] for x in comparison_data]
            differences = [x[3] for x in comparison_data]
            
            report.append(f"  Overall GPT Average: {statistics.mean(gpt_averages):.2f}")
            report.append(f"  Overall Gemini Average: {statistics.mean(gemini_averages):.2f}")
            report.append(f"  Average Difference (GPT - Gemini): {statistics.mean(differences):.2f}")
            report.append(f"  Std Dev of Differences: {statistics.stdev(differences) if len(differences) > 1 else 0:.2f}")
        
        report.append("")
        report.append("=" * 80)
        report.append("END OF ANALYSIS")
        report.append("=" * 80)
        
        return "\n".join(report)

    def export_to_csv(self, csv_path: Path):
        """Export summary statistics to CSV file."""
        try:
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Write header
                writer.writerow([
                    'Model', 'Evaluator', 'Count', 'Average', 'Std_Dev',
                    'Min', 'Max', 'Median', 'Individual_Scores'
                ])

                # Write GPT scores
                for model in sorted(self.gpt_scores.keys()):
                    scores = self.gpt_scores[model]
                    stats = self.calculate_statistics(scores)
                    writer.writerow([
                        model, 'GPT', stats['count'], f"{stats['average']:.2f}",
                        f"{stats['std_dev']:.2f}", stats['min'], stats['max'],
                        stats['median'], ';'.join(map(str, sorted(scores, reverse=True)))
                    ])

                # Write Gemini scores
                for model in sorted(self.gemini_scores.keys()):
                    scores = self.gemini_scores[model]
                    stats = self.calculate_statistics(scores)
                    writer.writerow([
                        model, 'Gemini', stats['count'], f"{stats['average']:.2f}",
                        f"{stats['std_dev']:.2f}", stats['min'], stats['max'],
                        stats['median'], ';'.join(map(str, sorted(scores, reverse=True)))
                    ])

                # Write additional evaluator scores
                for evaluator_name in sorted(self.additional_scores.keys()):
                    for model in sorted(self.additional_scores[evaluator_name].keys()):
                        scores = self.additional_scores[evaluator_name][model]
                        stats = self.calculate_statistics(scores)
                        writer.writerow([
                            model, evaluator_name, stats['count'], f"{stats['average']:.2f}",
                            f"{stats['std_dev']:.2f}", stats['min'], stats['max'],
                            stats['median'], ';'.join(map(str, sorted(scores, reverse=True)))
                        ])

        except Exception as e:
            print(f"Error: Failed to export CSV: {e}")
            raise


def main():
    """Main function to run the evaluation analysis."""
    parser = argparse.ArgumentParser(
        description="Analyze evaluation results from GPT and Gemini evaluators",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic analysis with default files
  python analyze_evaluation_results.py

  # Custom input files with text output
  python analyze_evaluation_results.py --gpt-file results/gpt_evaluation.jsonl --output analysis_report.txt

  # Include additional evaluation files and export CSV
  python analyze_evaluation_results.py --additional-files results/gpt_evaluation_gemini_flash.jsonl --csv-output summary.csv

  # Comprehensive analysis with all outputs
  python analyze_evaluation_results.py --additional-files results/*.jsonl --output full_report.txt --csv-output full_summary.csv
        """
    )
    
    parser.add_argument(
        '--gpt-file',
        type=Path,
        default=Path('results/gpt_evaluation.jsonl'),
        help='Path to GPT evaluation file (default: results/gpt_evaluation.jsonl)'
    )
    
    parser.add_argument(
        '--gemini-file',
        type=Path,
        default=Path('results/gemini_evaluation.jsonl'),
        help='Path to Gemini evaluation file (default: results/gemini_evaluation.jsonl)'
    )

    parser.add_argument(
        '--additional-files',
        type=Path,
        nargs='*',
        help='Additional evaluation files to process (auto-detects format)'
    )

    parser.add_argument(
        '--output',
        type=Path,
        help='Output file path (default: print to stdout)'
    )

    parser.add_argument(
        '--csv-output',
        type=Path,
        help='Export summary statistics to CSV file'
    )
    
    args = parser.parse_args()
    
    # Initialize analyzer
    analyzer = EvaluationAnalyzer()
    
    # Process files
    print("Processing evaluation files...")

    gpt_count = analyzer.process_gpt_file(args.gpt_file)
    print(f"Processed {gpt_count} GPT evaluations from {args.gpt_file}")

    gemini_count = analyzer.process_gemini_file(args.gemini_file)
    print(f"Processed {gemini_count} Gemini evaluations from {args.gemini_file}")

    # Process additional files
    additional_count = 0
    if args.additional_files:
        for additional_file in args.additional_files:
            evaluator_name = additional_file.stem.replace('_evaluation', '').replace('_', '-')
            count = analyzer.process_additional_file(additional_file, evaluator_name)
            print(f"Processed {count} {evaluator_name} evaluations from {additional_file}")
            additional_count += count

    if gpt_count == 0 and gemini_count == 0 and additional_count == 0:
        print("Error: No evaluation data found in any file.")
        sys.exit(1)
    
    # Generate report
    print("\nGenerating analysis report...")
    report = analyzer.generate_report()
    
    # Output report
    if args.output:
        try:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"Analysis report saved to: {args.output}")
        except Exception as e:
            print(f"Error: Failed to write output file: {e}")
            sys.exit(1)
    else:
        print("\n" + report)

    # Export CSV if requested
    if args.csv_output:
        try:
            analyzer.export_to_csv(args.csv_output)
            print(f"CSV export saved to: {args.csv_output}")
        except Exception as e:
            print(f"Error: Failed to export CSV: {e}")
            sys.exit(1)


if __name__ == "__main__":
    main()
