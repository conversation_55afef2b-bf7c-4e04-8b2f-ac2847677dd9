#!/bin/bash
# Usage examples for the batch evaluation system

echo "=== Batch Evaluation System Usage Examples ==="
echo

echo "1. List all available models in the dataset:"
echo "python3 format_for_batch_api.py --list-models"
echo

echo "2. Generate OpenAI batch requests for all models:"
echo "python3 format_for_batch_api.py"
echo "# Output: batch_evaluation_requests.jsonl (1,728 requests)"
echo

echo "3. Generate OpenAI batch requests for specific models:"
echo "python3 format_for_batch_api.py --models \"DeepSeek-R1-Distill-Llama-8B\" \"gpt-4o-2024-11-20\" --output selected_openai.jsonl"
echo

echo "4. Generate Gemini batch requests for all models:"
echo "python3 format_for_batch_api.py --api-format gemini"
echo "# Output: batch_evaluation_requests_gemini.jsonl"
echo

echo "5. Generate Gemini batch requests for specific models:"
echo "python3 format_for_batch_api.py --api-format gemini --models \"DeepSeek-R1-Distill-Llama-8B\" --output selected_gemini.jsonl"
echo

echo "6. Validate OpenAI batch file:"
echo "python3 validate_batch_file.py"
echo

echo "7. Validate Gemini batch file:"
echo "python3 validate_gemini_batch.py batch_evaluation_requests_gemini.jsonl"
echo

echo "=== Popular Model Combinations ==="
echo

echo "Top performing models (example):"
echo "python3 format_for_batch_api.py --models \"gpt-4o-2024-11-20\" \"claude-3-7-sonnet-20250219\" \"deepseek-r1\" \"gemini-2.5-pro\""
echo

echo "DeepSeek family:"
echo "python3 format_for_batch_api.py --models \"DeepSeek-R1-Distill-Llama-8B\" \"DeepSeek-R1-Distill-Qwen-32B\" \"deepseek-r1\" \"deepseek-v3\""
echo

echo "Qwen family:"
echo "python3 format_for_batch_api.py --models \"Qwen2.5-72B-Instruct\" \"Qwen3-30B-A3B\" \"Qwen3-14B\""
echo

echo "=== File Outputs ==="
echo "OpenAI format: batch_evaluation_requests.jsonl"
echo "Gemini format: batch_evaluation_requests_gemini.jsonl"
echo "Custom output: Use --output filename.jsonl"
echo

echo "=== Cost Estimation ==="
echo "All models (1,728 requests): ~$15-25 USD (OpenAI GPT-4)"
echo "Single model (~64 requests): ~$0.5-1 USD"
echo "Gemini pricing may vary - check current rates"
