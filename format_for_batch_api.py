#!/usr/bin/env python3
"""
Script to format evaluation data for OpenAI Batch API submission.
Processes merged_output_final.jsonl and creates batch API requests.
"""

import json
import sys

def load_evaluation_prompt():
    """Load the evaluation prompt from file."""
    try:
        with open('evaluation_prompt.txt', 'r') as f:
            return f.read().strip()
    except FileNotFoundError:
        print("Error: evaluation_prompt.txt not found")
        sys.exit(1)

def extract_model_answer(response_text):
    """Extract the final answer from the model response."""
    # Look for <answer> tags first
    if '<answer>' in response_text and '</answer>' in response_text:
        start = response_text.find('<answer>') + len('<answer>')
        end = response_text.find('</answer>')
        return response_text[start:end].strip()
    
    # If no answer tags, try to find the last substantial line
    lines = response_text.strip().split('\n')
    for line in reversed(lines):
        line = line.strip()
        if line and not line.startswith('<') and len(line) > 10:
            return line
    
    # Fallback: return last 200 characters
    return response_text[-200:].strip()

def create_evaluation_message(prompt, target, response, evaluation_prompt):
    """Create the evaluation message for OpenAI API."""
    # model_answer = extract_model_answer(response)
    model_answer = response

    evaluation_input = f"""Question: {prompt}

Target Answer: {target}

Model Response: {model_answer}"""

    return [
        {
            "role": "system",
            "content": evaluation_prompt
        },
        {
            "role": "user",
            "content": evaluation_input
        }
    ]

def create_gemini_evaluation_message(prompt, target, response, evaluation_prompt):
    """Create the evaluation message for Gemini API."""
    # model_answer = extract_model_answer(response)
    model_answer = response

    evaluation_input = f"""{evaluation_prompt}

Question: {prompt}

Target Answer: {target}

Model Response: {model_answer}"""

    return [{"parts": [{"text": evaluation_input}]}]

def get_available_models(input_file):
    """Get list of unique models in the dataset."""
    models = set()

    with open(input_file, 'r') as f:
        for line in f:
            try:
                data = json.loads(line.strip())
                model_name = data.get('model', '')
                if model_name:
                    models.add(model_name)
            except json.JSONDecodeError:
                continue

    return sorted(list(models))

def create_openai_batch_request(record_id, model_name, messages):
    """Create OpenAI batch request format."""
    return {
        "custom_id": f"eval-{record_id}-{model_name}",
        "method": "POST",
        "url": "/v1/chat/completions",
        "body": {
            "model": "gpt-4.1-2025-04-14",
            "messages": messages,
            "max_tokens": 2048,
            "temperature": 0.1,
            "n": 3
        }
    }

def create_gemini_batch_request(record_id, model_name, contents):
    """Create Gemini batch request format."""
    return {
        "key": f"eval-{record_id}-{model_name}",
        "request": {
            "contents": contents,
            "generation_config": {
                "temperature": 0.1,
                "maxOutputTokens": 2048,
                "candidateCount": 3,
                "thinkingConfig":{
                    "thinkingBudget": 1024,
                    "includeThoughts": False
                }
                
            }
        }
    }

def process_jsonl_to_batch_format(input_file, output_file, models=None, api_format="openai"):
    """Process JSONL file and create batch API format for OpenAI or Gemini."""
    evaluation_prompt = load_evaluation_prompt()

    batch_requests = []

    print(f"Processing {input_file}...")
    print(f"API format: {api_format}")
    if models:
        print(f"Filtering for models: {models}")

    with open(input_file, 'r') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())

                # Extract required fields
                prompt = data.get('prompt', '')
                target = data.get('target', '')
                response = data.get('response', '')
                record_id = data.get('id', f'unknown_{line_num}')
                model_name = data.get('model', '')

                # Filter by models if specified
                if models and model_name not in models:
                    continue

                # Create batch request based on API format
                if api_format.lower() == "gemini":
                    contents = create_gemini_evaluation_message(prompt, target, response, evaluation_prompt)
                    batch_request = create_gemini_batch_request(record_id, model_name, contents)
                else:  # Default to OpenAI format
                    messages = create_evaluation_message(prompt, target, response, evaluation_prompt)
                    batch_request = create_openai_batch_request(record_id, model_name, messages)

                batch_requests.append(batch_request)

                if line_num % 100 == 0:
                    print(f"Processed {line_num} records...")

            except json.JSONDecodeError as e:
                print(f"Error parsing line {line_num}: {e}")
                continue
            except Exception as e:
                print(f"Error processing line {line_num}: {e}")
                continue
    
    # Write batch requests to output file
    print(f"Writing {len(batch_requests)} batch requests to {output_file}...")
    
    with open(output_file, 'w') as f:
        for request in batch_requests:
            f.write(json.dumps(request) + '\n')
    
    print(f"Successfully created {len(batch_requests)} batch requests")
    print(f"Output saved to: {output_file}")
    
    # Print sample request for verification
    if batch_requests:
        print("\nSample batch request:")
        print(json.dumps(batch_requests[0], indent=2))

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Format evaluation data for OpenAI or Gemini Batch API')
    parser.add_argument('--input', default='merged_output_final.jsonl', help='Input JSONL file')
    parser.add_argument('--output', default='batch_evaluation_requests.jsonl', help='Output JSONL file')
    parser.add_argument('--models', nargs='*', help='List of models to evaluate (e.g., --models "DeepSeek-R1-Distill-Llama-8B" "gpt-4")')
    parser.add_argument('--list-models', action='store_true', help='List available models in the dataset and exit')
    parser.add_argument('--api-format', choices=['openai', 'gemini'], default='openai', help='API format: openai or gemini (default: openai)')

    args = parser.parse_args()

    input_file = args.input

    # List available models if requested
    if args.list_models:
        available_models = get_available_models(input_file)
        print("Available models in the dataset:")
        for model in available_models:
            print(f"  - {model}")
        print(f"\nTotal: {len(available_models)} models")
        sys.exit(0)

    output_file = args.output
    models = args.models
    api_format = args.api_format

    # Auto-adjust output filename if using default and different API format
    if args.output == 'batch_evaluation_requests.jsonl' and api_format == 'gemini':
        output_file = 'batch_evaluation_requests_gemini.jsonl'

    # Show available models if specific models are requested
    if models:
        available_models = get_available_models(input_file)
        invalid_models = [m for m in models if m not in available_models]
        if invalid_models:
            print(f"Warning: The following models are not found in the dataset: {invalid_models}")
            print("Available models:")
            for model in available_models:
                print(f"  - {model}")
            print()

    process_jsonl_to_batch_format(input_file, output_file, models, api_format)

# Qwen2.5-72B-Instruct Qwen3-30B-A3B Qwen3-14B gemma-3-27b-it deepseek-r1 deepseek-v3 gemini-2.5-pro gpt-4o-2024-11-20 claude-3-7-sonnet-20250219 DeepSeek-R1-Distill-Qwen-32B 