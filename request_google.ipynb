from google import genai
from google.genai import types
import os

# os.environ[""] = "AIzaSyDKXQVpntZl8a-DTbW4YD80b8nfj2I7_jE"

client = genai.Client(api_key="AIzaSyDKXQVpntZl8a-DTbW4YD80b8nfj2I7_jE")


uploaded_file = client.files.upload(
    file='batch_evaluation_selected_models_gemini.jsonl',
    config=types.UploadFileConfig(display_name='batch-evaluation', mime_type='jsonl')
)

print(f"Uploaded file: {uploaded_file.name}")

list(client.batches.list(config=types.ListBatchJobsConfig(page_size=100)))


# Assumes `uploaded_file` is the file object from the previous step
file_batch_job = client.batches.create(
    model="gemini-2.5-pro",
    src=uploaded_file.name,
    config={
        'display_name': "file-upload-job-1",
    },
)
-
print(f"Created batch job: {file_batch_job.name}")