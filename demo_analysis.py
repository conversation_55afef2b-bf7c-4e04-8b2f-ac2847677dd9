#!/usr/bin/env python3
"""
Demo script showing how to use the evaluation analysis script.
"""

import subprocess
import sys
from pathlib import Path

def run_demo():
    """Run demonstration of the evaluation analysis script."""
    
    print("=" * 60)
    print("EVALUATION RESULTS ANALYSIS - DEMONSTRATION")
    print("=" * 60)
    print()
    
    # Check if the main script exists
    script_path = Path("analyze_evaluation_results.py")
    if not script_path.exists():
        print("Error: analyze_evaluation_results.py not found!")
        sys.exit(1)
    
    # Demo 1: Basic analysis
    print("Demo 1: Basic analysis with default files")
    print("-" * 40)
    try:
        result = subprocess.run([
            sys.executable, "analyze_evaluation_results.py",
            "--output", "demo_basic_report.txt"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✓ Basic analysis completed successfully")
            print(f"  Output saved to: demo_basic_report.txt")
        else:
            print("✗ Basic analysis failed")
            print(f"  Error: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("✗ Basic analysis timed out")
    except Exception as e:
        print(f"✗ Basic analysis error: {e}")
    
    print()
    
    # Demo 2: Comprehensive analysis with CSV export
    print("Demo 2: Comprehensive analysis with CSV export")
    print("-" * 40)
    try:
        result = subprocess.run([
            sys.executable, "analyze_evaluation_results.py",
            "--additional-files", "results/gpt_evaluation_gemini_flash.jsonl",
            "--output", "demo_comprehensive_report.txt",
            "--csv-output", "demo_summary.csv"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✓ Comprehensive analysis completed successfully")
            print(f"  Text report saved to: demo_comprehensive_report.txt")
            print(f"  CSV summary saved to: demo_summary.csv")
        else:
            print("✗ Comprehensive analysis failed")
            print(f"  Error: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("✗ Comprehensive analysis timed out")
    except Exception as e:
        print(f"✗ Comprehensive analysis error: {e}")
    
    print()
    
    # Demo 3: Show help
    print("Demo 3: Display help information")
    print("-" * 40)
    try:
        result = subprocess.run([
            sys.executable, "analyze_evaluation_results.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ Help information:")
            print(result.stdout)
        else:
            print("✗ Failed to display help")
    except Exception as e:
        print(f"✗ Help display error: {e}")
    
    print()
    print("=" * 60)
    print("DEMONSTRATION COMPLETED")
    print("=" * 60)
    print()
    print("Generated files:")
    for filename in ["demo_basic_report.txt", "demo_comprehensive_report.txt", "demo_summary.csv"]:
        if Path(filename).exists():
            print(f"  ✓ {filename}")
        else:
            print(f"  ✗ {filename} (not created)")

if __name__ == "__main__":
    run_demo()
