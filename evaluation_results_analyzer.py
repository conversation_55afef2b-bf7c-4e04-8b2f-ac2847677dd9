#!/usr/bin/env python3
"""
Evaluation Results Analyzer

This script extracts and analyzes evaluation results from GPT and Gemini evaluators.
It processes the evaluation data to:
1. Extract scores by model for each evaluator
2. Calculate averages for each tested model
3. Organize output by evaluator
4. Generate formatted summary statistics
"""

import json
import re
import statistics
from collections import defaultdict
from pathlib import Path
import argparse


def extract_score_from_text(text):
    """Extract numerical score from evaluation text using regex patterns."""
    # Look for "Score: X/10" pattern
    score_pattern = r"Score:\s*(\d+(?:\.\d+)?)/10"
    match = re.search(score_pattern, text, re.IGNORECASE)
    if match:
        return float(match.group(1))
    
    # Look for "X/10" pattern at end of text
    score_pattern = r"(\d+(?:\.\d+)?)/10"
    matches = re.findall(score_pattern, text)
    if matches:
        return float(matches[-1])  # Take the last occurrence
    
    return None


def parse_gpt_evaluation_file(file_path):
    """Parse GPT evaluation results from JSONL file."""
    results = defaultdict(lambda: defaultdict(list))

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                data = json.loads(line.strip())

                # Extract custom_id to get model name
                custom_id = data.get('custom_id', '')
                if not custom_id.startswith('eval-'):
                    continue

                # Parse custom_id: eval-{question_id}-{model_name}
                # Split by '-' and find the model name (everything after the second dash)
                parts = custom_id.split('-', 2)  # Split into max 3 parts
                if len(parts) < 3:
                    continue

                model_name = parts[2]  # Everything after eval-{question_id}-

                # Extract scores from all choices
                response_body = data.get('response', {}).get('body', {})
                choices = response_body.get('choices', [])

                for choice in choices:
                    content = choice.get('message', {}).get('content', '')
                    score = extract_score_from_text(content)
                    if score is not None:
                        results['GPT'][model_name].append(score)

            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error parsing GPT line: {e}")
                continue

    return results


def parse_gemini_evaluation_file(file_path):
    """Parse Gemini evaluation results from JSONL file."""
    results = defaultdict(lambda: defaultdict(list))

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                data = json.loads(line.strip())

                # Extract key to get model name
                key = data.get('key', '')
                if not key.startswith('eval-'):
                    continue

                # Parse key: eval-{question_id}-{model_name}
                # Split by '-' and find the model name (everything after the second dash)
                parts = key.split('-', 2)  # Split into max 3 parts
                if len(parts) < 3:
                    continue

                model_name = parts[2]  # Everything after eval-{question_id}-

                # Extract scores from all candidates
                response = data.get('response', {})
                candidates = response.get('candidates', [])

                for candidate in candidates:
                    content_parts = candidate.get('content', {}).get('parts', [])
                    for part in content_parts:
                        text = part.get('text', '')
                        score = extract_score_from_text(text)
                        if score is not None:
                            results['Gemini'][model_name].append(score)

            except (json.JSONDecodeError, KeyError) as e:
                print(f"Error parsing Gemini line: {e}")
                continue

    return results


def calculate_model_averages(evaluator_results):
    """Calculate average scores for each model by evaluator."""
    averages = {}
    
    for evaluator, models in evaluator_results.items():
        averages[evaluator] = {}
        for model, scores in models.items():
            if scores:
                averages[evaluator][model] = {
                    'average': statistics.mean(scores),
                    'count': len(scores),
                    'std_dev': statistics.stdev(scores) if len(scores) > 1 else 0.0,
                    'min': min(scores),
                    'max': max(scores)
                }
    
    return averages


def print_detailed_results(evaluator_results, averages):
    """Print detailed results organized by evaluator."""
    
    for evaluator in sorted(evaluator_results.keys()):
        print(f"\n{'='*60}")
        print(f"{evaluator.upper()} EVALUATIONS")
        print(f"{'='*60}")
        
        models = evaluator_results[evaluator]
        if not models:
            print("No evaluation data found.")
            continue
        
        # Sort models by average score (descending)
        sorted_models = sorted(
            models.keys(), 
            key=lambda m: averages[evaluator].get(m, {}).get('average', 0), 
            reverse=True
        )
        
        print(f"\n{evaluator} Individual Scores:")
        print("-" * 40)
        
        for model in sorted_models:
            scores = models[model]
            avg_data = averages[evaluator].get(model, {})
            
            print(f"\n{model}:")
            print(f"  Individual scores: {scores}")
            print(f"  Count: {avg_data.get('count', 0)}")
            print(f"  Average: {avg_data.get('average', 0):.2f}")
            print(f"  Std Dev: {avg_data.get('std_dev', 0):.2f}")
            print(f"  Range: {avg_data.get('min', 0):.1f} - {avg_data.get('max', 0):.1f}")
        
        print(f"\n{evaluator} Summary Statistics:")
        print("-" * 40)
        print(f"{'Model':<35} {'Avg Score':<10} {'Count':<8} {'Std Dev':<10}")
        print("-" * 65)
        
        for model in sorted_models:
            avg_data = averages[evaluator].get(model, {})
            avg_score = avg_data.get('average', 0)
            count = avg_data.get('count', 0)
            std_dev = avg_data.get('std_dev', 0)
            
            print(f"{model:<35} {avg_score:<10.2f} {count:<8} {std_dev:<10.2f}")


def print_comparison_summary(averages):
    """Print a comparison summary across evaluators."""
    print(f"\n{'='*80}")
    print("EVALUATOR COMPARISON SUMMARY")
    print(f"{'='*80}")
    
    # Get all unique models
    all_models = set()
    for evaluator_data in averages.values():
        all_models.update(evaluator_data.keys())
    
    if not all_models:
        print("No models found for comparison.")
        return
    
    # Sort models alphabetically
    sorted_models = sorted(all_models)
    
    print(f"\n{'Model':<35} {'GPT Avg':<12} {'Gemini Avg':<12} {'Difference':<12}")
    print("-" * 75)
    
    for model in sorted_models:
        gpt_avg = averages.get('GPT', {}).get(model, {}).get('average')
        gemini_avg = averages.get('Gemini', {}).get(model, {}).get('average')
        
        gpt_str = f"{gpt_avg:.2f}" if gpt_avg is not None else "N/A"
        gemini_str = f"{gemini_avg:.2f}" if gemini_avg is not None else "N/A"
        
        if gpt_avg is not None and gemini_avg is not None:
            diff = gpt_avg - gemini_avg
            diff_str = f"{diff:+.2f}"
        else:
            diff_str = "N/A"
        
        print(f"{model:<35} {gpt_str:<12} {gemini_str:<12} {diff_str:<12}")


def main():
    parser = argparse.ArgumentParser(description='Analyze evaluation results from GPT and Gemini evaluators')
    parser.add_argument('--gpt-file', default='results/gpt_evaluation.jsonl', 
                       help='Path to GPT evaluation results file')
    parser.add_argument('--gemini-file', default='results/gemini_evaluation.jsonl', 
                       help='Path to Gemini evaluation results file')
    parser.add_argument('--output', help='Output file to save results (optional)')
    
    args = parser.parse_args()
    
    # Initialize results dictionary
    all_results = defaultdict(lambda: defaultdict(list))
    
    # Parse GPT results if file exists
    if Path(args.gpt_file).exists():
        print(f"Processing GPT evaluation file: {args.gpt_file}")
        gpt_results = parse_gpt_evaluation_file(args.gpt_file)
        for evaluator, models in gpt_results.items():
            for model, scores in models.items():
                all_results[evaluator][model].extend(scores)
    else:
        print(f"GPT file not found: {args.gpt_file}")
    
    # Parse Gemini results if file exists
    if Path(args.gemini_file).exists():
        print(f"Processing Gemini evaluation file: {args.gemini_file}")
        gemini_results = parse_gemini_evaluation_file(args.gemini_file)
        for evaluator, models in gemini_results.items():
            for model, scores in models.items():
                all_results[evaluator][model].extend(scores)
    else:
        print(f"Gemini file not found: {args.gemini_file}")
    
    if not all_results:
        print("No evaluation data found in either file.")
        return
    
    # Calculate averages
    averages = calculate_model_averages(all_results)
    
    # Print results
    print_detailed_results(all_results, averages)
    print_comparison_summary(averages)
    
    # Save to file if requested
    if args.output:
        import sys
        from io import StringIO
        
        # Capture output
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()
        
        print_detailed_results(all_results, averages)
        print_comparison_summary(averages)
        
        sys.stdout = old_stdout
        
        # Write to file
        with open(args.output, 'w') as f:
            f.write(captured_output.getvalue())
        
        print(f"\nResults saved to: {args.output}")


if __name__ == "__main__":
    main()
