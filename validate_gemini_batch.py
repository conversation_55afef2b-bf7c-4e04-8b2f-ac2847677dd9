#!/usr/bin/env python3
"""
Validation script for Gemini Batch API requests file.
Checks format, required fields, and provides statistics.
"""

import json
import sys

def validate_gemini_batch_file(filename):
    """Validate the Gemini batch requests file."""
    print(f"Validating Gemini batch file: {filename}...")
    
    total_requests = 0
    valid_requests = 0
    keys = set()
    errors = []
    
    required_fields = ['key', 'request']
    required_request_fields = ['contents', 'generation_config']
    
    with open(filename, 'r') as f:
        for line_num, line in enumerate(f, 1):
            total_requests += 1
            
            try:
                request = json.loads(line.strip())
                
                # Check required top-level fields
                missing_fields = [field for field in required_fields if field not in request]
                if missing_fields:
                    errors.append(f"Line {line_num}: Missing fields: {missing_fields}")
                    continue
                
                # Check key uniqueness
                key = request.get('key')
                if key in keys:
                    errors.append(f"Line {line_num}: Duplicate key: {key}")
                else:
                    keys.add(key)
                
                # Check request structure
                req_body = request.get('request', {})
                missing_req_fields = [field for field in required_request_fields if field not in req_body]
                if missing_req_fields:
                    errors.append(f"Line {line_num}: Missing request fields: {missing_req_fields}")
                    continue
                
                # Check contents structure
                contents = req_body.get('contents', [])
                if not isinstance(contents, list) or len(contents) == 0:
                    errors.append(f"Line {line_num}: Invalid contents structure")
                    continue
                
                # Check parts structure
                if not contents[0].get('parts') or not isinstance(contents[0]['parts'], list):
                    errors.append(f"Line {line_num}: Invalid parts structure")
                    continue
                
                # Check for evaluation content
                text_content = contents[0]['parts'][0].get('text', '')
                if 'Question:' not in text_content or 'Target Answer:' not in text_content or 'Model Response:' not in text_content:
                    errors.append(f"Line {line_num}: Missing evaluation content structure")
                    continue
                
                # Check generation config
                gen_config = req_body.get('generation_config', {})
                if 'temperature' not in gen_config or 'max_output_tokens' not in gen_config:
                    errors.append(f"Line {line_num}: Missing generation config parameters")
                    continue
                
                valid_requests += 1
                
            except json.JSONDecodeError as e:
                errors.append(f"Line {line_num}: JSON decode error: {e}")
            except Exception as e:
                errors.append(f"Line {line_num}: Unexpected error: {e}")
    
    # Print results
    print(f"\n=== Validation Results ===")
    print(f"Total requests: {total_requests}")
    print(f"Valid requests: {valid_requests}")
    print(f"Invalid requests: {total_requests - valid_requests}")
    print(f"Unique keys: {len(keys)}")
    
    if errors:
        print(f"\n=== Errors Found ({len(errors)}) ===")
        for error in errors[:10]:  # Show first 10 errors
            print(f"  {error}")
        if len(errors) > 10:
            print(f"  ... and {len(errors) - 10} more errors")
    else:
        print("\n✅ All requests are valid!")
    
    # Sample request info
    if valid_requests > 0:
        print(f"\n=== Sample Request Info ===")
        with open(filename, 'r') as f:
            first_line = f.readline()
            sample = json.loads(first_line)
            gen_config = sample['request']['generation_config']
            print(f"Temperature: {gen_config.get('temperature', 'Not set')}")
            print(f"Max output tokens: {gen_config.get('max_output_tokens', 'Not set')}")
            print(f"Candidate count: {gen_config.get('candidate_count', 'Not set')}")
            print(f"Key format: {sample['key']}")
    
    return valid_requests == total_requests and len(errors) == 0

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Validate Gemini Batch API requests file')
    parser.add_argument('filename', nargs='?', default='batch_evaluation_requests_gemini.jsonl', 
                       help='Gemini batch file to validate')
    
    args = parser.parse_args()
    
    try:
        is_valid = validate_gemini_batch_file(args.filename)
        
        if is_valid:
            print(f"\n🎉 {args.filename} is ready for Gemini Batch API submission!")
            sys.exit(0)
        else:
            print(f"\n❌ {args.filename} has validation errors. Please fix before submission.")
            sys.exit(1)
    except FileNotFoundError:
        print(f"❌ File not found: {args.filename}")
        print("Generate a Gemini batch file first using:")
        print("python3 format_for_batch_api.py --api-format gemini")
        sys.exit(1)
